'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import QRCode from 'qrcode';
import { authClient, useSession } from '@/lib/auth-client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Shield, ShieldCheck, QrCode, Key } from 'lucide-react';

const enableSchema = z.object({
  password: z.string().min(1, 'Password is required'),
});

const verifySchema = z.object({
  code: z
    .string()
    .min(6, 'Code must be 6 digits')
    .max(6, 'Code must be 6 digits'),
});

const disableSchema = z.object({
  password: z.string().min(1, 'Password is required'),
  code: z
    .string()
    .min(6, 'Code must be 6 digits')
    .max(6, 'Code must be 6 digits'),
});

type EnableFormData = z.infer<typeof enableSchema>;
type VerifyFormData = z.infer<typeof verifySchema>;
type DisableFormData = z.infer<typeof disableSchema>;

export function TwoFactorSettings() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [qrCode, setQrCode] = useState('');
  const [qrCodeDataURL, setQrCodeDataURL] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [showSetup, setShowSetup] = useState(false);
  const [showVerification, setShowVerification] = useState(false);

  const enableForm = useForm<EnableFormData>({
    resolver: zodResolver(enableSchema),
    defaultValues: { password: '' },
  });

  const verifyForm = useForm<VerifyFormData>({
    resolver: zodResolver(verifySchema),
    defaultValues: { code: '' },
  });

  const disableForm = useForm<DisableFormData>({
    resolver: zodResolver(disableSchema),
    defaultValues: { password: '', code: '' },
  });

  // Generate QR code when qrCode URI is available
  useEffect(() => {
    if (qrCode) {
      QRCode.toDataURL(qrCode, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      })
        .then(url => {
          setQrCodeDataURL(url);
        })
        .catch(err => {
          console.error('Error generating QR code:', err);
        });
    }
  }, [qrCode]);

  const handleEnable2FA = async (data: EnableFormData) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const result = await authClient.twoFactor.enable({
        password: data.password,
      });

      if (result.error) {
        setError(result.error.message || 'Failed to enable 2FA');
      } else if (result.data) {
        console.log(result.data);
        setQrCode(result.data.totpURI);
        setBackupCodes(result.data.backupCodes);
        setShowSetup(true);
        setShowVerification(true);
        setSuccess('2FA setup initiated. Please scan the QR code and verify.');
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifySetup = async (data: VerifyFormData) => {
    setIsLoading(true);
    setError('');

    try {
      const result = await authClient.twoFactor.verifyTotp({
        code: data.code,
      });

      if (result.error) {
        setError(result.error.message || 'Verification failed');
      } else {
        setSuccess('2FA has been successfully enabled!');
        setShowVerification(false);
        setShowSetup(false);
        // Refresh session to get updated user data
        window.location.reload();
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisable2FA = async (data: DisableFormData) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // First verify the TOTP code
      const verifyResult = await authClient.twoFactor.verifyTotp({
        code: data.code,
      });

      if (verifyResult.error) {
        setError(verifyResult.error.message || 'Invalid verification code');
        return;
      }

      // If verification successful, proceed to disable 2FA
      const disableResult = await authClient.twoFactor.disable({
        password: data.password,
      });

      if (disableResult.error) {
        setError(disableResult.error.message || 'Failed to disable 2FA');
      } else {
        setSuccess('2FA has been successfully disabled');
        // Refresh session to get updated user data
        window.location.reload();
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const is2FAEnabled = session?.user?.twoFactorEnabled;

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          {is2FAEnabled ? (
            <ShieldCheck className='h-5 w-5 text-green-600' />
          ) : (
            <Shield className='h-5 w-5 text-gray-400' />
          )}
          Two-Factor Authentication
          {is2FAEnabled && <Badge variant='secondary'>Enabled</Badge>}
        </CardTitle>
        <CardDescription>
          {`Add an extra layer of security to your account with two-factor
          authentication. To disable 2FA, you'll need both your password and a
          verification code.`}
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        {error && (
          <Alert variant='destructive'>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {!is2FAEnabled && !showSetup && (
          <Form {...enableForm}>
            <form
              onSubmit={enableForm.handleSubmit(handleEnable2FA)}
              className='space-y-4'
            >
              <FormField
                control={enableForm.control}
                name='password'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Password</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type='password'
                        placeholder='Enter your current password'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Setting up...' : 'Enable 2FA'}
              </Button>
            </form>
          </Form>
        )}

        {showSetup && qrCode && (
          <div className='space-y-4'>
            <div className='text-center'>
              <QrCode className='h-8 w-8 mx-auto mb-2' />
              <h3 className='font-semibold'>Scan QR Code</h3>
              <p className='text-sm text-muted-foreground mb-4'>
                Scan this QR code with your authenticator app (Google
                Authenticator, Authy, etc.)
              </p>
              <div className='bg-white p-4 rounded-lg inline-block'>
                {qrCodeDataURL ? (
                  <Image
                    src={qrCodeDataURL}
                    alt='2FA QR Code'
                    width={200}
                    height={200}
                    className='w-[200px] h-[200px]'
                  />
                ) : (
                  <div className='w-[200px] h-[200px] bg-gray-200 animate-pulse rounded flex items-center justify-center'>
                    <span className='text-gray-500 text-sm'>
                      Generating QR Code...
                    </span>
                  </div>
                )}
              </div>
            </div>

            {backupCodes.length > 0 && (
              <div className='bg-yellow-50 p-4 rounded-lg'>
                <h4 className='font-semibold flex items-center gap-2 mb-2'>
                  <Key className='h-4 w-4' />
                  Backup Codes
                </h4>
                <p className='text-sm text-muted-foreground mb-2'>
                  Save these backup codes in a safe place. You can use them to
                  access your account if you lose your authenticator device.
                </p>
                <div className='grid grid-cols-2 gap-2 font-mono text-sm'>
                  {backupCodes.map((code, index) => (
                    <div key={index} className='bg-white p-2 rounded border'>
                      {code}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {showVerification && (
              <Form {...verifyForm}>
                <form
                  onSubmit={verifyForm.handleSubmit(handleVerifySetup)}
                  className='space-y-4'
                >
                  <FormField
                    control={verifyForm.control}
                    name='code'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Verification Code</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Enter 6-digit code from your app'
                            maxLength={6}
                            autoComplete='one-time-code'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type='submit' disabled={isLoading}>
                    {isLoading ? 'Verifying...' : 'Verify and Enable 2FA'}
                  </Button>
                </form>
              </Form>
            )}
          </div>
        )}

        {is2FAEnabled && (
          <div className='space-y-4'>
            <Form {...disableForm}>
              <form
                onSubmit={disableForm.handleSubmit(handleDisable2FA)}
                className='space-y-4'
              >
                <FormField
                  control={disableForm.control}
                  name='password'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Password</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type='password'
                          placeholder='Enter your current password'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={disableForm.control}
                  name='code'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verification Code</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder='Enter 6-digit code from your authenticator app'
                          maxLength={6}
                          autoComplete='one-time-code'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type='submit'
                  variant='destructive'
                  disabled={isLoading}
                >
                  {isLoading ? 'Disabling...' : 'Disable 2FA'}
                </Button>
              </form>
            </Form>
          </div>
        )}

        {backupCodes.length > 0 && !showSetup && (
          <div className='bg-yellow-50 p-4 rounded-lg'>
            <h4 className='font-semibold flex items-center gap-2 mb-2'>
              <Key className='h-4 w-4' />
              Your New Backup Codes
            </h4>
            <div className='grid grid-cols-2 gap-2 font-mono text-sm'>
              {backupCodes.map((code, index) => (
                <div key={index} className='bg-white p-2 rounded border'>
                  {code}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
