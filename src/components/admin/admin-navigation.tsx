'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { LayoutDashboard, Users, Activity } from 'lucide-react';

export function AdminNavigation() {
  const pathname = usePathname();

  const navItems = [
    {
      title: 'Dashboard',
      href: `/admin`,
      icon: LayoutDashboard,
    },
    {
      title: 'Users',
      href: `/admin/users`,
      icon: Users,
    },
    {
      title: 'Sessions',
      href: `/admin/sessions`,
      icon: Activity,
    },
  ];

  return (
    <nav className='flex items-center space-x-4 lg:space-x-6'>
      {navItems.map(item => {
        const Icon = item.icon;
        const isActive = pathname === item.href;

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              'flex items-center text-sm font-medium transition-colors hover:text-primary',
              isActive ? 'text-foreground' : 'text-muted-foreground'
            )}
          >
            <Icon className='h-4 w-4 mr-2' />
            {item.title}
          </Link>
        );
      })}
    </nav>
  );
}
