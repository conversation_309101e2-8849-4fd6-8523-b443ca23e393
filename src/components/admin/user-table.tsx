'use client';

import { useState } from 'react';
import { authClient } from '@/lib/auth-client';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Search, UserPlus } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { UserDetailsDialog } from './user-details-dialog';
import { UserWithRole } from 'better-auth/plugins';

export function UserTable() {
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(0);
  const queryClient = useQueryClient();
  const limit = 10;

  const { data: usersData, isLoading } = useQuery({
    queryKey: ['admin-users', search, page],
    queryFn: async () => {
      const result = await authClient.admin.listUsers({
        query: {
          limit,
          offset: page * limit,
          ...(search && {
            searchField: 'email' as const,
            searchOperator: 'contains' as const,
            searchValue: search,
          }),
        },
      });
      return result;
    },
  });

  const banUserMutation = useMutation({
    mutationFn: async ({
      userId,
      reason,
    }: {
      userId: string;
      reason?: string;
    }) => {
      return authClient.admin.banUser({
        userId,
        banReason: reason || 'No reason provided',
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      toast.success('User banned successfully');
    },
    onError: error => {
      toast.error('Failed to ban user: ' + error.message);
    },
  });

  const unbanUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      return authClient.admin.unbanUser({ userId });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      toast.success('User unbanned successfully');
    },
    onError: error => {
      toast.error('Failed to unban user: ' + error.message);
    },
  });

  const setRoleMutation = useMutation({
    mutationFn: async ({ userId, role }: { userId: string; role: string }) => {
      return authClient.admin.setRole({ userId, role: role as any });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      toast.success('User role updated successfully');
    },
    onError: error => {
      toast.error('Failed to update user role: ' + error.message);
    },
  });
  const usersArray = (usersData?.data?.users as UserWithRole[]) || [];

  const totalCount = usersData?.data?.total || 0;
  const totalPages = Math.ceil(totalCount / limit);

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle>Users</CardTitle>
          <Button size='sm'>
            <UserPlus className='h-4 w-4 mr-2' />
            Add User
          </Button>
        </div>
        <div className='flex items-center space-x-2'>
          <div className='relative flex-1 max-w-sm'>
            <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search users...'
              value={search}
              onChange={e => setSearch(e.target.value)}
              className='pl-8'
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className='text-center py-8'>Loading users...</div>
        ) : (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {usersArray.map((user: UserWithRole) => (
                  <TableRow key={user.id}>
                    <TableCell className='font-medium'>{user.id}</TableCell>
                    <TableCell className='font-medium'>{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          user.role === 'admin' ? 'default' : 'secondary'
                        }
                      >
                        {user.role || 'user'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {user.banned ? (
                        <Badge variant='destructive'>Banned</Badge>
                      ) : (
                        <Badge variant='outline'>Active</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(user.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center gap-2'>
                        <UserDetailsDialog user={user} />
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant='ghost' className='h-8 w-8 p-0'>
                              <MoreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            {user.banned ? (
                              <DropdownMenuItem
                                onClick={() =>
                                  unbanUserMutation.mutate(user.id)
                                }
                              >
                                Unban User
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem
                                onClick={() =>
                                  banUserMutation.mutate({ userId: user.id })
                                }
                              >
                                Ban User
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={() =>
                                setRoleMutation.mutate({
                                  userId: user.id,
                                  role:
                                    user.role === 'admin' ? 'user' : 'admin',
                                })
                              }
                            >
                              {user.role === 'admin'
                                ? 'Remove Admin'
                                : 'Make Admin'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {totalPages > 1 && (
              <div className='flex items-center justify-between space-x-2 py-4'>
                <div className='text-sm text-muted-foreground'>
                  Showing {page * limit + 1} to{' '}
                  {Math.min((page + 1) * limit, totalCount)} of {totalCount}{' '}
                  users
                </div>
                <div className='flex space-x-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => setPage(page - 1)}
                    disabled={page === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => setPage(page + 1)}
                    disabled={page >= totalPages - 1}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
