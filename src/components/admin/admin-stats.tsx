'use client';

import { useQuery } from '@tanstack/react-query';
import { authClient } from '@/lib/auth-client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Shield, Activity, Ban } from 'lucide-react';

export function AdminStats() {
  const { data: usersData } = useQuery({
    queryKey: ['admin-stats-users'],
    queryFn: async () => {
      return authClient.admin.listUsers({
        query: { limit: 1000 }, // Get a large number to count
      });
    },
  });

  const { data: adminUsersData } = useQuery({
    queryKey: ['admin-stats-admins'],
    queryFn: async () => {
      return authClient.admin.listUsers({
        query: {
          limit: 1000,
          filterField: 'role',
          filterOperator: 'eq',
          filterValue: 'admin',
        },
      });
    },
  });

  const { data: bannedUsersData } = useQuery({
    queryKey: ['admin-stats-banned'],
    queryFn: async () => {
      return authClient.admin.listUsers({
        query: {
          limit: 1000,
          filterField: 'banned',
          filterOperator: 'eq',
          filterValue: 'true',
        },
      });
    },
  });

  const totalUsers = usersData?.data?.total || 0;
  const adminUsers = adminUsersData?.data?.total || 0;
  const bannedUsers = bannedUsersData?.data?.total || 0;
  const activeUsers = totalUsers - bannedUsers;

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Total Users</CardTitle>
          <Users className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{totalUsers}</div>
          <p className='text-xs text-muted-foreground'>
            Registered users in the system
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Admin Users</CardTitle>
          <Shield className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{adminUsers}</div>
          <p className='text-xs text-muted-foreground'>
            Users with admin privileges
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Active Users</CardTitle>
          <Activity className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{activeUsers}</div>
          <p className='text-xs text-muted-foreground'>
            Non-banned active users
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Banned Users</CardTitle>
          <Ban className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{bannedUsers}</div>
          <p className='text-xs text-muted-foreground'>
            Users currently banned
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
