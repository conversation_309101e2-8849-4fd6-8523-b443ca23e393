'use client';

import { useState } from 'react';
import { authClient } from '@/lib/auth-client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Eye, UserX, UserCheck, Shield, ShieldOff } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { UserWithRole } from 'better-auth/plugins';

interface UserDetailsDialogProps {
  user: UserWithRole;
  trigger?: React.ReactNode;
}

export function UserDetailsDialog({ user, trigger }: UserDetailsDialogProps) {
  const [open, setOpen] = useState(false);
  const [banReason, setBanReason] = useState('');
  const queryClient = useQueryClient();

  const { data: sessions } = useQuery({
    queryKey: ['user-sessions', user.id],
    queryFn: async () => {
      return authClient.admin.listUserSessions({ userId: user.id });
    },
    enabled: open,
  });

  const banUserMutation = useMutation({
    mutationFn: async (reason: string) => {
      return authClient.admin.banUser({
        userId: user.id,
        banReason: reason || 'No reason provided',
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      toast.success('User banned successfully');
      setOpen(false);
    },
    onError: error => {
      toast.error('Failed to ban user: ' + error.message);
    },
  });

  const unbanUserMutation = useMutation({
    mutationFn: async () => {
      return authClient.admin.unbanUser({ userId: user.id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      toast.success('User unbanned successfully');
      setOpen(false);
    },
    onError: error => {
      toast.error('Failed to unban user: ' + error.message);
    },
  });

  const toggleRoleMutation = useMutation({
    mutationFn: async () => {
      const newRole = user.role === 'admin' ? 'user' : 'admin';
      return authClient.admin.setRole({
        userId: user.id,
        role: newRole as any,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      toast.success('User role updated successfully');
      setOpen(false);
    },
    onError: error => {
      toast.error('Failed to update user role: ' + error.message);
    },
  });

  const impersonateUserMutation = useMutation({
    mutationFn: async () => {
      return authClient.admin.impersonateUser({ userId: user.id });
    },
    onSuccess: () => {
      toast.success('Impersonation started - refreshing page...');
      setTimeout(() => window.location.reload(), 1000);
    },
    onError: error => {
      toast.error('Failed to impersonate user: ' + error.message);
    },
  });

  const activeSessions = (sessions as any)?.length || 0;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant='ghost' size='sm'>
            <Eye className='h-4 w-4' />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle>User Details</DialogTitle>
          <DialogDescription>
            Manage user account and permissions
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* User Info */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Account Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <Label>Name</Label>
                  <div className='font-medium'>{user.name}</div>
                </div>
                <div>
                  <Label>Email</Label>
                  <div className='font-medium'>{user.email}</div>
                </div>
                <div>
                  <Label>Role</Label>
                  <Badge
                    variant={user.role === 'admin' ? 'default' : 'secondary'}
                  >
                    {user.role || 'user'}
                  </Badge>
                </div>
                <div>
                  <Label>Status</Label>
                  {user.banned ? (
                    <Badge variant='destructive'>Banned</Badge>
                  ) : (
                    <Badge variant='outline'>Active</Badge>
                  )}
                </div>
                <div>
                  <Label>Created</Label>
                  <div>{new Date(user.createdAt).toLocaleDateString()}</div>
                </div>
                <div>
                  <Label>Active Sessions</Label>
                  <div>{activeSessions}</div>
                </div>
              </div>

              {user.banned && user.banReason && (
                <div>
                  <Label>Ban Reason</Label>
                  <div className='text-sm text-muted-foreground'>
                    {user.banReason}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Actions</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex flex-wrap gap-2'>
                {user.banned ? (
                  <Button
                    variant='outline'
                    onClick={() => unbanUserMutation.mutate()}
                    disabled={unbanUserMutation.isPending}
                  >
                    <UserCheck className='h-4 w-4 mr-2' />
                    Unban User
                  </Button>
                ) : (
                  <>
                    <Button
                      variant='outline'
                      onClick={() => impersonateUserMutation.mutate()}
                      disabled={impersonateUserMutation.isPending}
                    >
                      <Eye className='h-4 w-4 mr-2' />
                      Impersonate
                    </Button>
                    <Button
                      variant='outline'
                      onClick={() => toggleRoleMutation.mutate()}
                      disabled={toggleRoleMutation.isPending}
                    >
                      {user.role === 'admin' ? (
                        <>
                          <ShieldOff className='h-4 w-4 mr-2' />
                          Remove Admin
                        </>
                      ) : (
                        <>
                          <Shield className='h-4 w-4 mr-2' />
                          Make Admin
                        </>
                      )}
                    </Button>
                  </>
                )}
              </div>

              {!user.banned && (
                <div className='space-y-2'>
                  <Label htmlFor='ban-reason'>Ban User</Label>
                  <Textarea
                    id='ban-reason'
                    placeholder='Reason for banning this user...'
                    value={banReason}
                    onChange={e => setBanReason(e.target.value)}
                  />
                  <Button
                    variant='destructive'
                    onClick={() => banUserMutation.mutate(banReason)}
                    disabled={banUserMutation.isPending}
                  >
                    <UserX className='h-4 w-4 mr-2' />
                    Ban User
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
