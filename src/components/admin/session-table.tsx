'use client';

import { useState } from 'react';
import { authClient } from '@/lib/auth-client';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Search, Trash2 } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface Session {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  createdAt: Date;
  ipAddress: string | null;
  userAgent: string | null;
  impersonatedBy: string | null;
  user?: {
    name: string;
    email: string;
  };
}

export function SessionTable() {
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const queryClient = useQueryClient();

  const { data: sessions, isLoading } = useQuery({
    queryKey: ['admin-sessions', selectedUserId],
    queryFn: async () => {
      if (!selectedUserId) return [];
      const result = await authClient.admin.listUserSessions({
        userId: selectedUserId,
      });
      console.log(result);
      return result;
    },
    enabled: !!selectedUserId,
  });

  const revokeSessionMutation = useMutation({
    mutationFn: async (sessionToken: string) => {
      return authClient.admin.revokeUserSession({ sessionToken });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-sessions'] });
      toast.success('Session revoked successfully');
    },
    onError: error => {
      toast.error('Failed to revoke session: ' + error.message);
    },
  });

  const revokeAllSessionsMutation = useMutation({
    mutationFn: async (userId: string) => {
      return authClient.admin.revokeUserSessions({ userId });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-sessions'] });
      toast.success('All sessions revoked successfully');
    },
    onError: error => {
      toast.error('Failed to revoke sessions: ' + error.message);
    },
  });

  const formatUserAgent = (userAgent: string | null) => {
    if (!userAgent) return 'Unknown';

    // Simple user agent parsing
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';

    return 'Other';
  };

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle>User Sessions</CardTitle>
          {selectedUserId && (
            <Button
              variant='destructive'
              size='sm'
              onClick={() => revokeAllSessionsMutation.mutate(selectedUserId)}
            >
              <Trash2 className='h-4 w-4 mr-2' />
              Revoke All Sessions
            </Button>
          )}
        </div>
        <div className='flex items-center space-x-2'>
          <div className='relative flex-1 max-w-sm'>
            <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Enter User ID to view sessions...'
              value={selectedUserId}
              onChange={e => setSelectedUserId(e.target.value)}
              className='pl-8'
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {!selectedUserId ? (
          <div className='text-center py-8 text-muted-foreground'>
            Enter a User ID to view their sessions
          </div>
        ) : isLoading ? (
          <div className='text-center py-8'>Loading sessions...</div>
        ) : !sessions || (sessions as any)?.length === 0 ? (
          <div className='text-center py-8 text-muted-foreground'>
            No sessions found for this user
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Session ID</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead>Browser</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className='w-[70px]'>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {(
                (sessions as any)?.data?.sessions ||
                (sessions as any) ||
                []
              )?.map((session: Session) => (
                <TableRow key={session.id}>
                  <TableCell className='font-mono text-xs'>
                    {session.id.substring(0, 8)}...
                  </TableCell>
                  <TableCell>{session.ipAddress || 'Unknown'}</TableCell>
                  <TableCell>{formatUserAgent(session.userAgent)}</TableCell>
                  <TableCell>
                    {new Date(session.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {new Date(session.expiresAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {session.impersonatedBy ? (
                      <Badge variant='outline'>Impersonated</Badge>
                    ) : new Date(session.expiresAt) > new Date() ? (
                      <Badge variant='default'>Active</Badge>
                    ) : (
                      <Badge variant='secondary'>Expired</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' className='h-8 w-8 p-0'>
                          <MoreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        <DropdownMenuItem
                          onClick={() =>
                            revokeSessionMutation.mutate(session.token)
                          }
                          className='text-destructive'
                        >
                          Revoke Session
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
