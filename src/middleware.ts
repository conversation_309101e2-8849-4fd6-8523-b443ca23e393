import createMiddleware from 'next-intl/middleware';
import { routing } from '@/i18n/routing';
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';

const intlMiddleware = createMiddleware(routing);

export default async function middleware(request: NextRequest) {
  // Skip internationalization for auth API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    return;
  }

  // Check for admin routes
  const isAdminRoute = request.nextUrl.pathname.includes('/admin');

  if (isAdminRoute) {
    try {
      const session = await auth.api.getSession({
        headers: request.headers,
      });

      // If no session, redirect to login
      if (!session?.user) {
        const locale = request.nextUrl.pathname.split('/')[1];
        return NextResponse.redirect(new URL(`/${locale}/login`, request.url));
      }

      // If not admin, redirect to home
      if (session.user.role !== 'admin') {
        const locale = request.nextUrl.pathname.split('/')[1];
        return NextResponse.redirect(new URL(`/${locale}`, request.url));
      }
    } catch (error) {
      console.error(error);
      // If auth check fails, redirect to login
      const locale = request.nextUrl.pathname.split('/')[1];
      return NextResponse.redirect(new URL(`/${locale}/login`, request.url));
    }
  }

  return intlMiddleware(request);
}

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
