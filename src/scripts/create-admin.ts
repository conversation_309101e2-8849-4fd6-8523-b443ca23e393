/**
 * <PERSON><PERSON><PERSON> to create an admin user
 * Run with: npx tsx src/scripts/create-admin.ts
 */

import { auth } from '@/lib/auth';

async function createAdminUser() {
  try {
    const adminUser = await auth.api.signUpEmail({
      body: {
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123456',
      },
    });

    if (adminUser) {
      console.log('User created successfully!');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123456');
      console.log('');
      console.log('To make this user an admin, run the following SQL command:');
      console.log(
        `UPDATE "user" SET role = 'admin' WHERE email = '<EMAIL>';`
      );
      console.log('');
      console.log('Please change the password after first login.');
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

createAdminUser();
