# Better Auth Admin Integration

This document describes the admin functionality that has been integrated into your Next.js project using Better Auth.

## Features Implemented

### 1. Admin Plugin Configuration

- ✅ Server-side admin plugin configured in `src/lib/auth.ts`
- ✅ Client-side admin plugin configured in `src/lib/auth-client.ts`
- ✅ Database schema already includes admin fields (role, banned, banReason, banExpires)

### 2. Admin Routes & Pages

- ✅ `/admin` - Admin dashboard with real-time statistics
- ✅ `/admin/users` - User management with table view
- ✅ `/admin/users/create` - Create new users
- ✅ `/admin/sessions` - Session management

### 3. Admin UI Components

- ✅ `AdminStats` - Real-time dashboard statistics
- ✅ `UserTable` - User management table with pagination and search
- ✅ `CreateUserForm` - Form to create new users with role assignment
- ✅ `SessionTable` - Session management table
- ✅ `UserDetailsDialog` - Detailed user management modal
- ✅ `AdminNavigation` - Admin navigation component

### 4. Admin Functionality

- ✅ List users with search and pagination
- ✅ Ban/unban users with custom reasons
- ✅ Change user roles (user/admin)
- ✅ Create new users with role assignment
- ✅ View and revoke user sessions
- ✅ User impersonation
- ✅ Real-time statistics dashboard
- ✅ Detailed user information modal
- ✅ Session management per user

### 5. Security & Authorization

- ✅ Middleware protection for admin routes
- ✅ Role-based access control
- ✅ Admin link in user dropdown (only for admin users)
- ✅ Secure session validation
- ✅ Proper error handling for unauthorized access

### 6. Enhanced Features

- ✅ Toast notifications for all admin actions
- ✅ Loading states and error handling
- ✅ Responsive design with shadcn/ui components
- ✅ Navigation between admin sections
- ✅ User details modal with comprehensive actions
- ✅ Real-time data updates

## Getting Started

### 1. Create Your First Admin User

Since admin routes are protected, you'll need to create your first admin user manually. You have a few options:

#### Option A: Direct Database Update

1. Create a regular user account through the normal signup process
2. Update the user's role in the database:

```sql
UPDATE "user" SET role = 'admin' WHERE email = '<EMAIL>';
```

#### Option B: Use the Auth API (Recommended)

1. Temporarily modify the admin layout to allow access without admin role
2. Create a user through the admin interface
3. Restore the admin protection

#### Option C: Environment Variable (if configured)

If you have `adminUserIds` configured in your auth setup, you can add your user ID to that array.

### 2. Access Admin Dashboard

Once you have an admin user:

1. Sign in with your admin account
2. Click on your user avatar in the top right
3. Select "Admin Dashboard" from the dropdown
4. You'll be redirected to `/admin`

## Admin Capabilities

### User Management

- **View Users**: Paginated table with search functionality
- **Create Users**: Form to create new users with role assignment
- **Ban/Unban**: Temporarily disable user accounts
- **Role Management**: Promote users to admin or demote to regular user
- **User Statistics**: Real-time counts of total, admin, active, and banned users

### Session Management

- **View Sessions**: See all sessions for a specific user
- **Revoke Sessions**: End individual sessions or all sessions for a user
- **Session Details**: IP address, browser, creation date, expiration

### Dashboard

- **Statistics Overview**: Quick stats on users and system status
- **Quick Actions**: Direct links to common admin tasks

## File Structure

```
src/
├── app/[locale]/admin/
│   ├── layout.tsx              # Admin layout with protection & navigation
│   ├── page.tsx                # Admin dashboard with real-time stats
│   ├── users/
│   │   ├── page.tsx            # User management page
│   │   └── create/
│   │       └── page.tsx        # Create user page
│   └── sessions/
│       └── page.tsx            # Session management page
├── components/admin/
│   ├── admin-stats.tsx         # Real-time dashboard statistics
│   ├── user-table.tsx          # User management table with actions
│   ├── create-user-form.tsx    # User creation form
│   ├── session-table.tsx       # Session management table
│   ├── user-details-dialog.tsx # Detailed user management modal
│   └── admin-navigation.tsx    # Admin navigation component
├── components/ui/
│   ├── table.tsx               # Table components (added)
│   ├── select.tsx              # Select component (added)
│   └── textarea.tsx            # Textarea component (added)
├── lib/
│   ├── auth.ts                 # Server auth config (admin plugin)
│   └── auth-client.ts          # Client auth config (admin plugin)
├── middleware.ts               # Route protection with admin checks
└── scripts/
    └── create-admin.ts         # Helper script for creating admin users
```

## Dependencies Added

- `sonner` - Toast notifications for admin actions
- `@radix-ui/react-select` - Select component for forms
- `shadcn/ui table` - Table components for data display

## Security Notes

1. **Route Protection**: All admin routes are protected by middleware
2. **Role Verification**: Both client and server verify admin role
3. **Session Validation**: Admin actions require valid authentication
4. **Error Handling**: Graceful handling of unauthorized access attempts

## Customization

### Adding New Admin Features

1. Create new components in `src/components/admin/`
2. Add new routes under `src/app/[locale]/admin/`
3. Update navigation in the admin dashboard

### Styling

All components use shadcn/ui design system and are fully customizable through Tailwind CSS.

### Permissions

The current implementation uses simple role-based access (admin/user). For more complex permissions, refer to the Better Auth documentation on custom access control.

## Troubleshooting

### "Access Denied" Issues

- Verify user has `role: 'admin'` in the database
- Check middleware is not blocking the request
- Ensure session is valid and not expired

### Admin Link Not Showing

- Verify user role is exactly 'admin' (case-sensitive)
- Check that the session includes the role field
- Refresh the page to update session data

### API Errors

- Check that Better Auth admin plugin is properly configured
- Verify database schema includes admin fields
- Check network requests in browser dev tools

## Next Steps

1. Create your first admin user
2. Test the admin functionality
3. Customize the UI to match your brand
4. Add additional admin features as needed
5. Set up proper monitoring and logging for admin actions

For more advanced features, refer to the [Better Auth Admin Plugin Documentation](https://www.better-auth.com/docs/plugins/admin).
